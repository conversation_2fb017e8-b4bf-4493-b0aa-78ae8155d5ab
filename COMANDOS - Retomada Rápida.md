# COMANDOS PARA RETOMADA RÁPIDA

## 🚀 COMANDO PRINCIPAL DE ATIVAÇÃO

```
<PERSON><PERSON>, analise, internalize, assuma a persona e execute o prompt descrito em G:\Meu Drive\Documentos\Obsidian Vault\4 - 👨‍💻 Prompts\19 - VS CODE - Material de Revisão.md

Continue a geração do material de revisão exatamente de onde foi interrompida, conforme detalhado no arquivo "CONTINUIDADE - Geração Material de Revisão.md".
```

## 🔍 COMANDOS DE VERIFICAÇÃO INICIAL

### 1. Verificar estado atual do arquivo destino
```
view("2 - 📝 Resumos/4 - Processo Civil - Fazenda Pública/02 - Prescrição.md", type="file", view_range=[70, 85])
```

### 2. Verificar progresso no plano
```
view("Plano - Material de Revisão - 02 - Prescrição.md", type="file", view_range=[90, 120])
```

### 3. Verificar próxima seção a processar
```
view("1 - 👨‍💼 Procurador/4 - Processo Civil - Fazenda Pública/02 - Prescrição.md", type="file", search_query_regex="^## 3 - NOVO CÓDIGO CIVIL", context_lines_after=30)
```

## 📝 SEQUÊNCIA DE AÇÕES PARA CONTINUAR

### Passo 1: Ler seção origem
```
view("1 - 👨‍💼 Procurador/4 - Processo Civil - Fazenda Pública/02 - Prescrição.md", type="file", search_query_regex="^## 3 - NOVO CÓDIGO CIVIL", context_lines_after=50)
```

### Passo 2: Aplicar prompt SER
- Carregar: `view("4 - 👨‍💻 Prompts/06 - Material de Revisão - PDF.md", type="file")`
- Processar conteúdo seguindo estrutura de 7 seções
- Aplicar classes CSS semânticas

### Passo 3: Adicionar ao arquivo destino
```
str-replace-editor("2 - 📝 Resumos/4 - Processo Civil - Fazenda Pública/02 - Prescrição.md", command="str_replace", old_str="[ÚLTIMA LINHA DO ARQUIVO]", new_str="[NOVA SEÇÃO FORMATADA]")
```

### Passo 4: Atualizar plano
```
str-replace-editor("Plano - Material de Revisão - 02 - Prescrição.md", command="str_replace", old_str="- [/] Parte 3:", new_str="- [x] Parte 3: ... - ✅ Concluído em [DATA]")
```

## 🎯 PRÓXIMAS PARTES A PROCESSAR (EM ORDEM)

1. **ATUAL**: Parte 3 - NOVO CÓDIGO CIVIL E PRAZO PRESCRICIONAL DAS AÇÕES DE INDENIZAÇÃO
2. Parte 4 - AÇÕES PROPOSTAS PELA FAZENDA PÚBLICA
3. Parte 5 - AÇÕES DE RESSARCIMENTO AO ERÁRIO
4. Parte 6 - PRESCRIÇÃO EM EXECUÇÕES PROPOSTAS EM FACE DA FAZENDA PÚBLICA
5. Parte 7 - PRESCRIÇÃO EM AÇÕES REPARATÓRIAS POR TORTURA
6. Parte 8 - PRESCRIÇÃO EM EXECUÇÃO DE MULTAS AMBIENTAIS
7. Parte 9 - SUSPENSÃO E INTERRUPÇÃO DO PRAZO PRESCRICIONAL
8. Parte 10 - POSSIBILIDADE DE ANÁLISE DE OFÍCIO PELO JUIZ
9. Parte 11 - PRESTAÇÕES DE TRATO SUCESSIVO X FUNDO DO DIREITO
10. Parte 12 - INTERVENÇÃO ANÔMALA
11. Parte 13 - DENUNCIAÇÃO DA LIDE
12. Parte 14 - REMESSA NECESSÁRIA
13. Parte 15 - RESUMO DA AULA

## 📋 TEMPLATE DE ESTRUTURA POR TÓPICO

```markdown
## [Nome do Tópico]

### Definição
[Conceito principal com <span class="definicao">]

### Espécies/Classificação
[Se aplicável - tipos, categorias]

### Características Principais
[Pontos fundamentais com classes apropriadas]

### Exemplos Relevantes
[Se houver - com <span class="exemplo">]

### Diferenciações Cruciais
[Comparações importantes com <span class="conceito-importante">]

### Atenção!
[Pegadinhas e exceções com <span class="atencao">]

### Elementos Adicionais
[Prazos, jurisprudência, dispositivos legais]
```

## 🎨 CLASSES CSS PRIORITÁRIAS

1. `<span class="atencao">` - Vedações, exceções, pegadinhas (VERMELHO)
2. `<span class="conceito-importante">` - Conceitos fundamentais (AMARELO+VERMELHO)
3. `<span class="definicao">` - Definições e regras gerais (VERDE)
4. `<span class="highlight-yellow-bold">` - Prazos, quóruns, valores (AMARELO)
5. `<span class="jurisprudencia">` - Jurisprudência (AZUL+SUBLINHADO)
6. `<span class="artigo-lei">` - Artigos de lei (ROXO)
7. `<span class="exemplo">` - Exemplos práticos (AZUL CLARO)

## ⚡ COMANDO DE EMERGÊNCIA

Se houver problemas ou dúvidas:

```
Consulte o arquivo "CONTINUIDADE - Geração Material de Revisão.md" para contexto completo e troubleshooting. 

Verifique o estado atual dos arquivos:
- Arquivo origem: 1 - 👨‍💼 Procurador\4 - Processo Civil - Fazenda Pública\02 - Prescrição.md
- Arquivo destino: 2 - 📝 Resumos\4 - Processo Civil - Fazenda Pública\02 - Prescrição.md
- Plano: Plano - Material de Revisão - 02 - Prescrição.md

Mantenha a formatação consistente e siga rigorosamente o prompt SER.
```

## 📊 CONTROLE DE QUALIDADE

### Checklist por seção processada:
- [ ] Estrutura de 7 seções aplicada (quando aplicável)
- [ ] Classes CSS semânticas utilizadas
- [ ] Conteúdo livre de citações
- [ ] Formatação Markdown pura (sem blocos de código)
- [ ] Otimizado para TTS
- [ ] Plano atualizado com progresso
- [ ] Próxima ação definida

### Validação final:
- [ ] Conteúdo autossuficiente
- [ ] Formatação consistente
- [ ] Destaques apropriados
- [ ] Estrutura lógica mantida
