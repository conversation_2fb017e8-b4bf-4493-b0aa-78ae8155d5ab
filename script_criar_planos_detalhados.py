#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criação de planos detalhados de material de revisão.
Baseado no prompt 19 - VS CODE - Material de Revisão.md
"""

import os
from pathlib import Path
from datetime import datetime

def analisar_estrutura_arquivo(arquivo_origem):
    """
    Analisa a estrutura do arquivo original para criar o plano.
    """
    with open(arquivo_origem, 'r', encoding='utf-8') as f:
        conteudo_original = f.read()

    linhas = conteudo_original.split('\n')
    topicos_principais = []
    topicos_secundarios = []

    for linha in linhas:
        linha = linha.strip()
        if linha.startswith('## '):
            topicos_principais.append(linha[3:])
        elif linha.startswith('### '):
            topicos_secundarios.append(linha[4:])

    return {
        'conteudo': conteudo_original,
        'topicos_principais': topicos_principais,
        'topicos_secundarios': topicos_secundarios,
        'tamanho': len(conteudo_original)
    }

def criar_plano_detalhado(arquivo_origem, nome_arquivo):
    """
    Cria plano detalhado para geração do material de revisão.
    """
    print(f"\n📋 Criando plano para: {nome_arquivo}")

    analise = analisar_estrutura_arquivo(arquivo_origem)

    complexidade = 'Alta' if analise['tamanho'] > 50000 else 'Média' if analise['tamanho'] > 20000 else 'Baixa'

    plano = f"""# PLANO DE MATERIAL DE REVISÃO - {nome_arquivo.upper()}

## INFORMAÇÕES GERAIS
- **Arquivo Origem**: {arquivo_origem}
- **Data de Criação**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
- **Status**: 🔄 EM ANDAMENTO

## ANÁLISE DO CONTEÚDO ORIGINAL

### Tópicos Principais Identificados
{chr(10).join([f"- {topico}" for topico in analise['topicos_principais']])}

### Subtópicos Identificados
{chr(10).join([f"- {topico}" for topico in analise['topicos_secundarios']])}

### Estimativa de Complexidade
- **Tópicos Principais**: {len(analise['topicos_principais'])}
- **Subtópicos**: {len(analise['topicos_secundarios'])}
- **Tamanho do Arquivo**: {analise['tamanho']:,} caracteres
- **Complexidade**: {complexidade}

## PLANO DE EXECUÇÃO

### Estratégia de Divisão
O material será processado em partes para garantir qualidade e completude:

{chr(10).join([f"**Parte {i+1}**: {topico}" for i, topico in enumerate(analise['topicos_principais'])])}

### Pontos Críticos para Concursos
- Identificar definições fundamentais
- Destacar exceções e pegadinhas
- Mapear jurisprudência relevante
- Extrair prazos e procedimentos
- Evidenciar diferenciações cruciais

## PROGRESSO DE EXECUÇÃO

### Status por Parte
{chr(10).join([f"- [ ] Parte {i+1}: {topico}" for i, topico in enumerate(analise['topicos_principais'])])}

### Observações de Progresso
- **Iniciado em**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
- **Última atualização**: Aguardando início
- **Próxima ação**: Iniciar Parte 1

## CONTROLE DE QUALIDADE

### Checklist Final
- [ ] Todos os tópicos originais foram abordados
- [ ] Formatação adequada para TTS aplicada
- [ ] Destaques semânticos corretos
- [ ] Estrutura de revisão completa
- [ ] Conteúdo autossuficiente
- [ ] Livre de referências externas

### Validação
- [ ] Revisão completa realizada
- [ ] Material adequado para concursos
- [ ] Formatação Obsidian correta

## INSTRUÇÕES PARA GERAÇÃO

### Prompt Base
Usar o prompt "06 - Material de Revisão - PDF.md" com as seguintes adaptações:
- **REMOVER**: Blocos de código (```) da saída
- **MANTER**: Formatação Markdown e classes CSS
- **APLICAR**: Estrutura de 7 seções por tópico

### Processo Incremental
1. Processar uma parte por vez conforme divisão acima
2. Salvar progresso parcial no arquivo de destino
3. Atualizar este plano com o progresso
4. Continuar para a próxima parte
5. Validar completude ao final
"""

    return plano

def main():
    """Função principal."""
    print("🚀 CRIAÇÃO DE PLANOS DETALHADOS - PROCESSO CIVIL FAZENDA PÚBLICA")
    print("=" * 80)

    # Lista dos arquivos que precisam de material de revisão
    arquivos_para_processar = [
        "02 - Prescrição",
        "03 - Responsabilidade Civil do Estado", 
        "04 - Tutela Provisória Contra a Fazenda Pública",
        "05 - Execução Fiscal",
        "06 - Execução Contra a Fazenda Pública e Precatórios",
        "07 - Ação Popular",
        "08 - Mandado de Segurança",
        "09 - Desapropriação",
        "10 - Juizados Especiais da Fazenda Pública",
        "11 - Improbidade Administrativa",
        "12 - Ação Civil Pública"
    ]

    origem_base = r"1 - 👨‍💼 Procurador\4 - Processo Civil - Fazenda Pública"
    
    planos_criados = 0

    for nome_arquivo in arquivos_para_processar:
        arquivo_origem = os.path.join(origem_base, nome_arquivo + ".md")
        
        if os.path.exists(arquivo_origem):
            try:
                plano = criar_plano_detalhado(arquivo_origem, nome_arquivo)
                
                # Salvar plano
                nome_plano = f"Plano - Material de Revisão - {nome_arquivo}.md"
                with open(nome_plano, 'w', encoding='utf-8') as f:
                    f.write(plano)
                
                print(f"✅ Plano criado: {nome_plano}")
                planos_criados += 1
                
            except Exception as e:
                print(f"❌ Erro ao criar plano para {nome_arquivo}: {e}")
        else:
            print(f"❌ Arquivo não encontrado: {arquivo_origem}")

    print(f"\n🎉 {planos_criados} planos criados com sucesso!")
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Revisar os planos gerados")
    print("2. Carregar o prompt base (06 - Material de Revisão - PDF.md)")
    print("3. Processar uma parte por vez de cada arquivo")
    print("4. Atualizar progresso nos planos")
    print("5. Validar completude final")

if __name__ == "__main__":
    main()
