# PLANO DE MATERIAL DE REVISÃO - 11 - <PERSON>MPROBIDADE ADMINISTRATIVA

## INFORMAÇÕES GERAIS
- **Arquivo Origem**: 1 - 👨‍💼 Procurador\4 - Processo Civil - Fazenda Pública\11 - Improbidade Administrativa.md
- **Data de Criação**: 03/07/2025 12:02
- **Status**: 🔄 EM ANDAMENTO

## ANÁLISE DO CONTEÚDO ORIGINAL

### Tópicos Principais Identificados
- 1 – INTRODUÇÃO
- 2 – A LEI 14.230/2021 E AS PRINCIPAIS ALTERAÇÕES NA LEI 8.429/92
- 4- ATOS DE IMPROBIDADE ADMINISTRATIVA
- 5 – SUJEITO ATIVO DO ATO DE IMPROBIDADE
- 6- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ATIVA PARA PROPOR A DEMANDA
- 7-PROCEDIMENTO
- 8 – TRANSAÇÃO EM AÇÕES DE IMPROBIDADE
- 9 – MEDIDAS CAUTELARES
- 10 - COMPETÊNCIA
- 11 – SANÇÕES, SENTENÇA E COISA JULGADA
- 12 – PRESCRIÇÃO
- JURISPRUDÊNCIA CORRELATA

### Subtópicos Identificados
- 2.1 - Natureza da ação de improbidade administrativa
- 2.2 - <PERSON>m da Modalidade Culposa
- 3.3 - <PERSON> Direito Administrativo Sancionador
- 3.4 - Non Bis In Idem
- 4.1 - Considerações Gerais
- 4.2 – Da efetiva demonstração de prejuízo ao erário
- 4.3 - Rol Taxativo ou Exemplificativo?
- 4.4 – Análise dos atos em espécie: Art. 9º
- 4.5 – Análise dos atos em espécie: Art. 10
- 4.6 – Análise dos atos em espécie: Art. 11
- 4.7 – Da Declaração de Bens
- 5.1 - Agentes públicos
- 5.2 - Agentes privados
- 5.3 – Situação dos sucessores e herdeiros dos agentes
- 5.4 – Situação das pessoas jurídicas de direito privado
- 9.1 - Indisponibilidade de Bens
- 9.2 - Afastamento do exercício de cargo, emprego ou função
- 11.1 – Improbidade de “menor valor ofensivo"
- 11.2 – Aplicação das Sanções
- 11.3 – Execução das Sanções

### Estimativa de Complexidade
- **Tópicos Principais**: 12
- **Subtópicos**: 20
- **Tamanho do Arquivo**: 201,635 caracteres
- **Complexidade**: Alta

## PLANO DE EXECUÇÃO

### Estratégia de Divisão
O material será processado em partes para garantir qualidade e completude:

**Parte 1**: 1 – INTRODUÇÃO
**Parte 2**: 2 – A LEI 14.230/2021 E AS PRINCIPAIS ALTERAÇÕES NA LEI 8.429/92
**Parte 3**: 4- ATOS DE IMPROBIDADE ADMINISTRATIVA
**Parte 4**: 5 – SUJEITO ATIVO DO ATO DE IMPROBIDADE
**Parte 5**: 6- LEGITIMIDADE ATIVA PARA PROPOR A DEMANDA
**Parte 6**: 7-PROCEDIMENTO
**Parte 7**: 8 – TRANSAÇÃO EM AÇÕES DE IMPROBIDADE
**Parte 8**: 9 – MEDIDAS CAUTELARES
**Parte 9**: 10 - COMPETÊNCIA
**Parte 10**: 11 – SANÇÕES, SENTENÇA E COISA JULGADA
**Parte 11**: 12 – PRESCRIÇÃO
**Parte 12**: JURISPRUDÊNCIA CORRELATA

### Pontos Críticos para Concursos
- Identificar definições fundamentais
- Destacar exceções e pegadinhas
- Mapear jurisprudência relevante
- Extrair prazos e procedimentos
- Evidenciar diferenciações cruciais

## PROGRESSO DE EXECUÇÃO

### Status por Parte
- [ ] Parte 1: 1 – INTRODUÇÃO
- [ ] Parte 2: 2 – A LEI 14.230/2021 E AS PRINCIPAIS ALTERAÇÕES NA LEI 8.429/92
- [ ] Parte 3: 4- ATOS DE IMPROBIDADE ADMINISTRATIVA
- [ ] Parte 4: 5 – SUJEITO ATIVO DO ATO DE IMPROBIDADE
- [ ] Parte 5: 6- LEGITIMIDADE ATIVA PARA PROPOR A DEMANDA
- [ ] Parte 6: 7-PROCEDIMENTO
- [ ] Parte 7: 8 – TRANSAÇÃO EM AÇÕES DE IMPROBIDADE
- [ ] Parte 8: 9 – MEDIDAS CAUTELARES
- [ ] Parte 9: 10 - COMPETÊNCIA
- [ ] Parte 10: 11 – SANÇÕES, SENTENÇA E COISA JULGADA
- [ ] Parte 11: 12 – PRESCRIÇÃO
- [ ] Parte 12: JURISPRUDÊNCIA CORRELATA

### Observações de Progresso
- **Iniciado em**: 03/07/2025 12:02
- **Última atualização**: Aguardando início
- **Próxima ação**: Iniciar Parte 1

## CONTROLE DE QUALIDADE

### Checklist Final
- [ ] Todos os tópicos originais foram abordados
- [ ] Formatação adequada para TTS aplicada
- [ ] Destaques semânticos corretos
- [ ] Estrutura de revisão completa
- [ ] Conteúdo autossuficiente
- [ ] Livre de referências externas

### Validação
- [ ] Revisão completa realizada
- [ ] Material adequado para concursos
- [ ] Formatação Obsidian correta

## INSTRUÇÕES PARA GERAÇÃO

### Prompt Base
Usar o prompt "06 - Material de Revisão - PDF.md" com as seguintes adaptações:
- **REMOVER**: Blocos de código (```) da saída
- **MANTER**: Formatação Markdown e classes CSS
- **APLICAR**: Estrutura de 7 seções por tópico

### Processo Incremental
1. Processar uma parte por vez conforme divisão acima
2. Salvar progresso parcial no arquivo de destino
3. Atualizar este plano com o progresso
4. Continuar para a próxima parte
5. Validar completude ao final
