#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para análise e criação de planos de material de revisão.
Baseado no prompt 19 - VS CODE - Material de Revisão.md
"""

import os
from pathlib import Path
from datetime import datetime

def analisar_origem_destino(origem, destino):
    """
    Analisa origem e destino para determinar o que precisa ser processado.
    """
    print("🔍 Analisando origem e destino...")
    print("=" * 60)

    # Processamento de pasta
    print(f"📁 Pasta origem: {origem}")
    print(f"📁 Pasta destino: {destino}")

    # Criar pasta de destino se não existir
    os.makedirs(destino, exist_ok=True)

    # Buscar arquivos .md na origem
    arquivos_origem = []
    for arquivo in os.listdir(origem):
        if arquivo.endswith('.md'):
            arquivos_origem.append(arquivo)

    print(f"📄 Encontrados {len(arquivos_origem)} arquivos na origem")

    # Verificar quais precisam de material de revisão
    arquivos_para_processar = []

    for arquivo in sorted(arquivos_origem):
        nome_base = Path(arquivo).stem
        caminho_origem = os.path.join(origem, arquivo)
        caminho_destino = os.path.join(destino, arquivo)

        if os.path.exists(caminho_destino):
            with open(caminho_destino, 'r', encoding='utf-8') as f:
                conteudo = f.read().strip()
            if len(conteudo) < 100:  # Arquivo vazio ou quase vazio
                print(f"⚠️  {nome_base} - Existe mas está vazio/incompleto ({len(conteudo)} chars)")
                arquivos_para_processar.append((caminho_origem, caminho_destino, nome_base))
            else:
                print(f"✅ {nome_base} - Material já existe e parece completo ({len(conteudo)} chars)")
        else:
            print(f"📝 {nome_base} - Precisa ser criado")
            arquivos_para_processar.append((caminho_origem, caminho_destino, nome_base))

    return arquivos_para_processar

def analisar_estrutura_arquivo(arquivo_origem):
    """
    Analisa a estrutura do arquivo original para criar o plano.
    """
    with open(arquivo_origem, 'r', encoding='utf-8') as f:
        conteudo_original = f.read()

    linhas = conteudo_original.split('\n')
    topicos_principais = []
    topicos_secundarios = []

    for linha in linhas:
        linha = linha.strip()
        if linha.startswith('## '):
            topicos_principais.append(linha[3:])
        elif linha.startswith('### '):
            topicos_secundarios.append(linha[4:])

    return {
        'conteudo': conteudo_original,
        'topicos_principais': topicos_principais,
        'topicos_secundarios': topicos_secundarios,
        'tamanho': len(conteudo_original)
    }

def main():
    """Função principal."""
    print("🚀 ANÁLISE PARA MATERIAL DE REVISÃO - PROCESSO CIVIL FAZENDA PÚBLICA")
    print("=" * 80)

    # Configuração conforme prompt
    origem = r"1 - 👨‍💼 Procurador\4 - Processo Civil - Fazenda Pública"
    destino = r"2 - 📝 Resumos\4 - Processo Civil - Fazenda Pública"

    arquivos_para_processar = analisar_origem_destino(origem, destino)

    if not arquivos_para_processar:
        print("\n🎉 Todos os materiais de revisão já estão criados!")
        return

    print(f"\n📊 RESUMO: {len(arquivos_para_processar)} arquivo(s) precisam de material de revisão")
    print("-" * 60)

    # Listar arquivos que precisam ser processados
    print("\n📋 ARQUIVOS PARA PROCESSAR:")
    for i, (origem_arquivo, destino_arquivo, nome) in enumerate(arquivos_para_processar, 1):
        print(f"{i:2d}. {nome}")
        
        # Análise rápida da estrutura
        try:
            analise = analisar_estrutura_arquivo(origem_arquivo)
            complexidade = 'Alta' if analise['tamanho'] > 50000 else 'Média' if analise['tamanho'] > 20000 else 'Baixa'
            print(f"    📏 Tamanho: {analise['tamanho']:,} chars | Complexidade: {complexidade}")
            print(f"    📑 Tópicos principais: {len(analise['topicos_principais'])} | Subtópicos: {len(analise['topicos_secundarios'])}")
        except Exception as e:
            print(f"    ❌ Erro ao analisar: {e}")
        print()

    print("🚀 Próximo passo: Criar planos detalhados para cada arquivo")

if __name__ == "__main__":
    main()
