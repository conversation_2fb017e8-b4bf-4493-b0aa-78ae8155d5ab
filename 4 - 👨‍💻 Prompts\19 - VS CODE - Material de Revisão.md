# PROMPT PARA GERAÇÃO AUTOMÁTICA DE MATERIAIS DE REVISÃO

## INSTRUÇÃO PRINCIPAL

Gere materiais de revisão de alta qualidade a partir de notas Obsidian originais, seguindo um processo estruturado de análise, planejamento e execução incremental.

**CONFIGURAÇÃO PARA USO:**

- **Arquivo/Pasta Origem**: G:\Meu Drive\Documentos\Obsidian Vault\1 - 👨‍💼 Procurador\4 - Processo Civil - Fazenda Pública
- **Arquivo/Pasta Destino**: G:\Meu Drive\Documentos\Obsidian Vault\2 - 📝 Resumos\4 - Processo Civil - Fazenda Pública

## ESPECIFICAÇÕES TÉCNICAS

### Estrutura de Trabalho

- **Arquivo de Plano**: G:\Meu Drive\Documentos\Obsidian Vault\Plano - Material de Revisão.md
- **Pasta de Destino**: Criada automaticamente se não existir
- **Formato de Saída**: Markdown puro (sem blocos de código)
- **Base de Formatação**: O Prompt descrito em G:\Meu Drive\Documentos\Obsidian Vault\4 - 👨‍💻 Prompts\06 - Material de Revisão - PDF.md

### Processo de Execução

#### Passo 1: Análise Inicial

```
1. Verificar se é arquivo único ou pasta/subpasta
2. Se for pasta: analisar arquivos existentes no destino
3. Identificar quais materiais de revisão precisam ser criados
4. Verificar arquivos em branco ou incompletos no destino
```

#### Passo 2: Criação do Plano

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para análise e criação de plano de material de revisão.
"""

import os
from pathlib import Path

def analisar_origem_destino():
    """
    Analisa origem e destino para determinar o que precisa ser processado.
    """
    # CONFIGURAR ESTAS VARIÁVEIS PARA CADA EXECUÇÃO
    origem = r"[CAMINHO_ORIGEM]"  # ALTERAR AQUI
    destino = r"[CAMINHO_DESTINO]"  # ALTERAR AQUI

    print("🔍 Analisando origem e destino...")
    print("=" * 60)

    # Verificar se origem é arquivo ou pasta
    if os.path.isfile(origem):
        # Processamento de arquivo único
        nome_arquivo = Path(origem).stem
        arquivo_destino = os.path.join(destino, nome_arquivo + '.md')
        plano_nome = f"Plano - Material de Revisão - {nome_arquivo}.md"

        print(f"📄 Arquivo origem: {origem}")
        print(f"📄 Arquivo destino: {arquivo_destino}")

        # Verificar se já existe material de revisão
        if os.path.exists(arquivo_destino):
            with open(arquivo_destino, 'r', encoding='utf-8') as f:
                conteudo = f.read().strip()
            if len(conteudo) < 100:  # Arquivo vazio ou quase vazio
                print("⚠️  Arquivo de destino existe mas está vazio/incompleto")
                return [(origem, arquivo_destino, nome_arquivo)]
            else:
                print("✅ Material de revisão já existe e parece completo")
                return []
        else:
            print("📝 Material de revisão precisa ser criado")
            return [(origem, arquivo_destino, nome_arquivo)]

    else:
        # Processamento de pasta
        print(f"📁 Pasta origem: {origem}")
        print(f"📁 Pasta destino: {destino}")

        # Criar pasta de destino se não existir
        os.makedirs(destino, exist_ok=True)

        # Buscar arquivos .md na origem
        arquivos_origem = []
        for arquivo in os.listdir(origem):
            if arquivo.endswith('.md'):
                arquivos_origem.append(arquivo)

        print(f"📄 Encontrados {len(arquivos_origem)} arquivos na origem")

        # Verificar quais precisam de material de revisão
        arquivos_para_processar = []

        for arquivo in sorted(arquivos_origem):
            nome_base = Path(arquivo).stem
            caminho_origem = os.path.join(origem, arquivo)
            caminho_destino = os.path.join(destino, arquivo)

            if os.path.exists(caminho_destino):
                with open(caminho_destino, 'r', encoding='utf-8') as f:
                    conteudo = f.read().strip()
                if len(conteudo) < 100:  # Arquivo vazio ou quase vazio
                    print(f"⚠️  {nome_base} - Existe mas está vazio/incompleto")
                    arquivos_para_processar.append((caminho_origem, caminho_destino, nome_base))
                else:
                    print(f"✅ {nome_base} - Material já existe e parece completo")
            else:
                print(f"📝 {nome_base} - Precisa ser criado")
                arquivos_para_processar.append((caminho_origem, caminho_destino, nome_base))

        return arquivos_para_processar

def criar_plano_detalhado(arquivo_origem, nome_arquivo):
    """
    Cria plano detalhado para geração do material de revisão.
    """
    print(f"\n📋 Criando plano para: {nome_arquivo}")

    # Ler arquivo original
    with open(arquivo_origem, 'r', encoding='utf-8') as f:
        conteudo_original = f.read()

    # Analisar estrutura do conteúdo
    linhas = conteudo_original.split('\n')
    topicos_principais = []
    topicos_secundarios = []

    for linha in linhas:
        linha = linha.strip()
        if linha.startswith('## '):
            topicos_principais.append(linha[3:])
        elif linha.startswith('### '):
            topicos_secundarios.append(linha[4:])

    # Criar plano estruturado
    plano = f"""# PLANO DE MATERIAL DE REVISÃO - {nome_arquivo.upper()}

## INFORMAÇÕES GERAIS
- **Arquivo Origem**: {arquivo_origem}
- **Data de Criação**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
- **Status**: 🔄 EM ANDAMENTO

## ANÁLISE DO CONTEÚDO ORIGINAL

### Tópicos Principais Identificados
{chr(10).join([f"- {topico}" for topico in topicos_principais])}

### Subtópicos Identificados
{chr(10).join([f"- {topico}" for topico in topicos_secundarios])}

### Estimativa de Complexidade
- **Tópicos Principais**: {len(topicos_principais)}
- **Subtópicos**: {len(topicos_secundarios)}
- **Tamanho do Arquivo**: {len(conteudo_original)} caracteres
- **Complexidade**: {'Alta' if len(conteudo_original) > 50000 else 'Média' if len(conteudo_original) > 20000 else 'Baixa'}

## PLANO DE EXECUÇÃO

### Estratégia de Divisão
O material será processado em partes para garantir qualidade e completude:

{chr(10).join([f"**Parte {i+1}**: {topico}" for i, topico in enumerate(topicos_principais)])}

### Pontos Críticos para Concursos
- Identificar definições fundamentais
- Destacar exceções e pegadinhas
- Mapear jurisprudência relevante
- Extrair prazos e procedimentos
- Evidenciar diferenciações cruciais

## PROGRESSO DE EXECUÇÃO

### Status por Parte
{chr(10).join([f"- [ ] Parte {i+1}: {topico}" for i, topico in enumerate(topicos_principais)])}

### Observações de Progresso
- **Iniciado em**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
- **Última atualização**: Aguardando início
- **Próxima ação**: Iniciar Parte 1

## CONTROLE DE QUALIDADE

### Checklist Final
- [ ] Todos os tópicos originais foram abordados
- [ ] Formatação adequada para TTS aplicada
- [ ] Destaques semânticos corretos
- [ ] Estrutura de revisão completa
- [ ] Conteúdo autossuficiente
- [ ] Livre de referências externas

### Validação
- [ ] Revisão completa realizada
- [ ] Material adequado para concursos
- [ ] Formatação Obsidian correta
"""

    return plano

def main():
    """Função principal."""
    from datetime import datetime

    arquivos_para_processar = analisar_origem_destino()

    if not arquivos_para_processar:
        print("\n🎉 Todos os materiais de revisão já estão criados!")
        return

    print(f"\n📊 RESUMO: {len(arquivos_para_processar)} arquivo(s) precisam de material de revisão")
    print("-" * 60)

    for origem, destino, nome in arquivos_para_processar:
        plano = criar_plano_detalhado(origem, nome)

        # Salvar plano
        nome_plano = f"Plano - Material de Revisão - {nome}.md"
        with open(nome_plano, 'w', encoding='utf-8') as f:
            f.write(plano)

        print(f"✅ Plano criado: {nome_plano}")

    print("\n🚀 Planos criados! Prossiga para a geração dos materiais.")

if __name__ == "__main__":
    main()
```

#### Passo 3: Geração Incremental do Material

Para cada arquivo identificado no plano:

1. **Ler o arquivo original** e o plano correspondente
2. **Aplicar o prompt base** (06 - Material de Revisão - PDF.md)
3. **Gerar uma parte por vez** seguindo a divisão do plano
4. **Salvar progresso parcial** no arquivo de destino
5. **Atualizar o plano** com o progresso realizado
6. **Continuar para a próxima parte** até completar todo o material

#### Passo 4: Adaptações do Prompt Base

**MODIFICAÇÕES CRÍTICAS ao prompt 06 - Material de Revisão - PDF.md:**

1. **MANTER** toda a formatação Markdown e classes CSS
2. **PRESERVAR** todas as diretrizes de conteúdo e estrutura
3. **ADAPTAR** para salvamento direto no Obsidian

#### Passo 5: Controle de Progresso

```markdown
## TEMPLATE DE ATUALIZAÇÃO DO PLANO

### Status por Parte

- [x] Parte 1: [TÓPICO] - ✅ Concluído em [DATA/HORA]
- [/] Parte 2: [TÓPICO] - 🔄 Em andamento
- [ ] Parte 3: [TÓPICO] - ⏳ Aguardando

### Observações de Progresso

- **Última atualização**: [DATA/HORA]
- **Parte atual**: [NÚMERO/NOME]
- **Próxima ação**: [DESCRIÇÃO]
- **Problemas encontrados**: [LISTA SE HOUVER]
```

## INSTRUÇÕES DE USO

### Configuração Inicial

1. **Alterar variáveis no script**:

   ```python
   origem = r"[CAMINHO_ORIGEM]"      # Ex: "2 - 📝 Resumos\3 - Processo Civil"
   destino = r"[CAMINHO_DESTINO]"    # Ex: "5 - 📚 Material de Revisão\3 - Processo Civil"
   ```

2. **Executar análise**: `python analisar_material_revisao.py`

### Fluxo de Trabalho

1. **Análise** → Identificar arquivos que precisam de material de revisão
2. **Planejamento** → Criar planos detalhados para cada arquivo
3. **Geração Incremental** → Processar uma parte por vez
4. **Atualização** → Registrar progresso no plano
5. **Validação** → Verificar completude e qualidade
6. **Finalização** → Marcar como concluído no plano

### Exemplo de Uso Completo

```
ORIGEM: "2 - 📝 Resumos\3 - Processo Civil\01 - Introdução.md"
DESTINO: "5 - 📚 Material de Revisão\3 - Processo Civil\01 - Introdução.md"
PLANO: "Plano - Material de Revisão - 01 - Introdução.md"

1. Executar script de análise
2. Revisar plano gerado
3. Aplicar prompt de geração (sem blocos de código)
4. Salvar primeira parte do material
5. Atualizar progresso no plano
6. Repetir até completar
7. Validação final
```

## DIRETRIZES ESPECÍFICAS

### Para Arquivo Único

- Criar plano detalhado com divisão por tópicos principais
- Gerar material incrementalmente
- Atualizar progresso a cada parte concluída

### Para Pasta/Subpasta

- Analisar todos os arquivos .md da origem
- Verificar arquivos existentes no destino
- Identificar arquivos vazios ou incompletos
- Processar apenas os que precisam de material de revisão
- Criar plano individual para cada arquivo

### Controle de Qualidade

- Verificar se todos os tópicos originais foram abordados
- Confirmar formatação adequada para Obsidian
- Validar estrutura de revisão completa
- Garantir conteúdo autossuficiente para concursos

## NOTAS IMPORTANTES

### Adaptação do Prompt Base

- **MANTER**: Todas as classes CSS e formatação Markdown
- **PRESERVAR**: Estrutura de 7 seções por tópico
- **ADAPTAR**: Para salvamento direto no Obsidian

### Gestão de Arquivos

- Sempre verificar arquivos existentes antes de processar
- Considerar arquivos com menos de 100 caracteres como vazios
- Manter backup dos planos para referência futura
- Atualizar status regularmente durante o processo

## SCRIPT COMPLETO DE EXECUÇÃO

### Script de Análise e Planejamento

````python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script completo para análise e criação de planos de material de revisão.
"""

import os
from pathlib import Path
from datetime import datetime

def analisar_origem_destino(origem, destino):
    """
    Analisa origem e destino para determinar o que precisa ser processado.
    """
    print("🔍 Analisando origem e destino...")
    print("=" * 60)

    # Verificar se origem é arquivo ou pasta
    if os.path.isfile(origem):
        # Processamento de arquivo único
        nome_arquivo = Path(origem).stem
        arquivo_destino = os.path.join(destino, nome_arquivo + '.md')

        print(f"📄 Arquivo origem: {origem}")
        print(f"📄 Arquivo destino: {arquivo_destino}")

        # Criar pasta de destino se não existir
        os.makedirs(os.path.dirname(arquivo_destino), exist_ok=True)

        # Verificar se já existe material de revisão
        if os.path.exists(arquivo_destino):
            with open(arquivo_destino, 'r', encoding='utf-8') as f:
                conteudo = f.read().strip()
            if len(conteudo) < 100:  # Arquivo vazio ou quase vazio
                print("⚠️  Arquivo de destino existe mas está vazio/incompleto")
                return [(origem, arquivo_destino, nome_arquivo)]
            else:
                print("✅ Material de revisão já existe e parece completo")
                return []
        else:
            print("📝 Material de revisão precisa ser criado")
            return [(origem, arquivo_destino, nome_arquivo)]

    else:
        # Processamento de pasta
        print(f"📁 Pasta origem: {origem}")
        print(f"📁 Pasta destino: {destino}")

        # Criar pasta de destino se não existir
        os.makedirs(destino, exist_ok=True)

        # Buscar arquivos .md na origem
        arquivos_origem = []
        for arquivo in os.listdir(origem):
            if arquivo.endswith('.md'):
                arquivos_origem.append(arquivo)

        print(f"📄 Encontrados {len(arquivos_origem)} arquivos na origem")

        # Verificar quais precisam de material de revisão
        arquivos_para_processar = []

        for arquivo in sorted(arquivos_origem):
            nome_base = Path(arquivo).stem
            caminho_origem = os.path.join(origem, arquivo)
            caminho_destino = os.path.join(destino, arquivo)

            if os.path.exists(caminho_destino):
                with open(caminho_destino, 'r', encoding='utf-8') as f:
                    conteudo = f.read().strip()
                if len(conteudo) < 100:  # Arquivo vazio ou quase vazio
                    print(f"⚠️  {nome_base} - Existe mas está vazio/incompleto")
                    arquivos_para_processar.append((caminho_origem, caminho_destino, nome_base))
                else:
                    print(f"✅ {nome_base} - Material já existe e parece completo")
            else:
                print(f"📝 {nome_base} - Precisa ser criado")
                arquivos_para_processar.append((caminho_origem, caminho_destino, nome_base))

        return arquivos_para_processar

def analisar_estrutura_arquivo(arquivo_origem):
    """
    Analisa a estrutura do arquivo original para criar o plano.
    """
    with open(arquivo_origem, 'r', encoding='utf-8') as f:
        conteudo_original = f.read()

    linhas = conteudo_original.split('\n')
    topicos_principais = []
    topicos_secundarios = []

    for linha in linhas:
        linha = linha.strip()
        if linha.startswith('## '):
            topicos_principais.append(linha[3:])
        elif linha.startswith('### '):
            topicos_secundarios.append(linha[4:])

    return {
        'conteudo': conteudo_original,
        'topicos_principais': topicos_principais,
        'topicos_secundarios': topicos_secundarios,
        'tamanho': len(conteudo_original)
    }

def criar_plano_detalhado(arquivo_origem, nome_arquivo):
    """
    Cria plano detalhado para geração do material de revisão.
    """
    print(f"\n📋 Criando plano para: {nome_arquivo}")

    analise = analisar_estrutura_arquivo(arquivo_origem)

    complexidade = 'Alta' if analise['tamanho'] > 50000 else 'Média' if analise['tamanho'] > 20000 else 'Baixa'

    plano = f"""# PLANO DE MATERIAL DE REVISÃO - {nome_arquivo.upper()}

## INFORMAÇÕES GERAIS
- **Arquivo Origem**: {arquivo_origem}
- **Data de Criação**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
- **Status**: 🔄 EM ANDAMENTO

## ANÁLISE DO CONTEÚDO ORIGINAL

### Tópicos Principais Identificados
{chr(10).join([f"- {topico}" for topico in analise['topicos_principais']])}

### Subtópicos Identificados
{chr(10).join([f"- {topico}" for topico in analise['topicos_secundarios']])}

### Estimativa de Complexidade
- **Tópicos Principais**: {len(analise['topicos_principais'])}
- **Subtópicos**: {len(analise['topicos_secundarios'])}
- **Tamanho do Arquivo**: {analise['tamanho']} caracteres
- **Complexidade**: {complexidade}

## PLANO DE EXECUÇÃO

### Estratégia de Divisão
O material será processado em partes para garantir qualidade e completude:

{chr(10).join([f"**Parte {i+1}**: {topico}" for i, topico in enumerate(analise['topicos_principais'])])}

### Pontos Críticos para Concursos
- Identificar definições fundamentais
- Destacar exceções e pegadinhas
- Mapear jurisprudência relevante
- Extrair prazos e procedimentos
- Evidenciar diferenciações cruciais

## PROGRESSO DE EXECUÇÃO

### Status por Parte
{chr(10).join([f"- [ ] Parte {i+1}: {topico}" for i, topico in enumerate(analise['topicos_principais'])])}

### Observações de Progresso
- **Iniciado em**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
- **Última atualização**: Aguardando início
- **Próxima ação**: Iniciar Parte 1

## CONTROLE DE QUALIDADE

### Checklist Final
- [ ] Todos os tópicos originais foram abordados
- [ ] Formatação adequada para TTS aplicada
- [ ] Destaques semânticos corretos
- [ ] Estrutura de revisão completa
- [ ] Conteúdo autossuficiente
- [ ] Livre de referências externas

### Validação
- [ ] Revisão completa realizada
- [ ] Material adequado para concursos
- [ ] Formatação Obsidian correta

## INSTRUÇÕES PARA GERAÇÃO

### Prompt Base
Usar o prompt "06 - Material de Revisão - PDF.md" com as seguintes adaptações:
- **REMOVER**: Blocos de código (```) da saída
- **MANTER**: Formatação Markdown e classes CSS
- **APLICAR**: Estrutura de 7 seções por tópico

### Processo Incremental
1. Processar uma parte por vez conforme divisão acima
2. Salvar progresso parcial no arquivo de destino
3. Atualizar este plano com o progresso
4. Continuar para a próxima parte
5. Validar completude ao final
"""

    return plano

def main():
    """Função principal."""
    print("🚀 GERADOR DE PLANOS PARA MATERIAL DE REVISÃO")
    print("=" * 60)

    # CONFIGURAR ESTAS VARIÁVEIS PARA CADA EXECUÇÃO
    origem = r"[CAMINHO_ORIGEM]"  # ALTERAR AQUI
    destino = r"[CAMINHO_DESTINO]"  # ALTERAR AQUI

    if origem == r"[CAMINHO_ORIGEM]" or destino == r"[CAMINHO_DESTINO]":
        print("❌ ERRO: Configure as variáveis 'origem' e 'destino' no script!")
        print("   origem = r'caminho/para/arquivo/ou/pasta'")
        print("   destino = r'caminho/para/destino'")
        return

    arquivos_para_processar = analisar_origem_destino(origem, destino)

    if not arquivos_para_processar:
        print("\n🎉 Todos os materiais de revisão já estão criados!")
        return

    print(f"\n📊 RESUMO: {len(arquivos_para_processar)} arquivo(s) precisam de material de revisão")
    print("-" * 60)

    for origem_arquivo, destino_arquivo, nome in arquivos_para_processar:
        plano = criar_plano_detalhado(origem_arquivo, nome)

        # Salvar plano
        nome_plano = f"Plano - Material de Revisão - {nome}.md"
        with open(nome_plano, 'w', encoding='utf-8') as f:
            f.write(plano)

        print(f"✅ Plano criado: {nome_plano}")

    print("\n🚀 Planos criados! Prossiga para a geração dos materiais.")
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Revisar os planos gerados")
    print("2. Aplicar o prompt de geração (sem blocos de código)")
    print("3. Processar uma parte por vez")
    print("4. Atualizar progresso nos planos")
    print("5. Validar completude final")

if __name__ == "__main__":
    main()
````

## TEMPLATE DE EXECUÇÃO MANUAL

### Para Arquivo Único

```
1. CONFIGURAR SCRIPT:
   origem = r"2 - 📝 Resumos\3 - Processo Civil\01 - Introdução.md"
   destino = r"5 - 📚 Material de Revisão\3 - Processo Civil\01 - Introdução.md"

2. EXECUTAR: python gerar_plano_revisao.py

3. REVISAR PLANO: "Plano - Material de Revisão - 01 - Introdução.md"

4. APLICAR PROMPT: Usar prompt 06 sem blocos de código

5. GERAR INCREMENTALMENTE: Uma parte por vez

6. ATUALIZAR PROGRESSO: Marcar partes concluídas no plano
```

### Para Pasta Completa

```
1. CONFIGURAR SCRIPT:
   origem = r"2 - 📝 Resumos\3 - Processo Civil"
   destino = r"5 - 📚 Material de Revisão\3 - Processo Civil"

2. EXECUTAR: python gerar_plano_revisao.py

3. REVISAR PLANOS: Múltiplos arquivos "Plano - Material de Revisão - *.md"

4. PROCESSAR SEQUENCIALMENTE: Um arquivo por vez

5. ATUALIZAR PROGRESSOS: Manter controle em cada plano
```


